-- Example hierarchical UI scene
local Scene = require("client/ui/controls/scene")
local Panel = require("client/ui/controls/panel")
local Button = require("client/ui/controls/button")
local Label = require("client/ui/controls/label")

return function()
	-- Create root scene
	local rootScene = Scene()
	rootScene.props.name = "MainMenu"
	rootScene.props.backgroundColor = { 0.1, 0.1, 0.15, 1 }

	-- Create main panel
	local mainPanel = Panel()
	mainPanel.props.title = "Main Menu"
	mainPanel.props.layout = "vertical"
	mainPanel.props.backgroundColor = { 0.2, 0.2, 0.25, 0.9 }
	mainPanel.props.borderColor = { 0.4, 0.4, 0.5, 1 }
	mainPanel.props.padding = { top = 30, right = 20, bottom = 20, left = 20 }
	mainPanel.props.spacing = 10

	-- Create title label
	local titleLabel = Label()
	titleLabel.props.text = "LuaStation13"
	titleLabel.props.color = { 1, 1, 1, 1 }
	titleLabel.props.align = "center"
	titleLabel.props.shadow = true

	-- Create buttons
	local playButton = Button()
	playButton.props.text = "Play"
	playButton.props.onClick = function()
		print("Play button clicked!")
	end

	local optionsButton = Button()
	optionsButton.props.text = "Options"
	optionsButton.props.onClick = function()
		print("Options button clicked!")
	end

	local exitButton = Button()
	exitButton.props.text = "Exit"
	exitButton.props.backgroundColor = { 0.5, 0.2, 0.2, 1 }
	exitButton.props.hoverColor = { 0.6, 0.3, 0.3, 1 }
	exitButton.props.onClick = function()
		print("Exit button clicked!")
		love.event.quit()
	end

	-- Add children to main panel
	LS13.Util.PrintTable(getmetatable(mainPanel))
	mainPanel:addChild(titleLabel)
	mainPanel:addChild(playButton)
	mainPanel:addChild(optionsButton)
	mainPanel:addChild(exitButton)

	-- Add main panel to root scene (centered)
	rootScene:addChild(mainPanel, {
		x = 100,
		y = 100,
		w = 300,
		h = 400
	})

	-- Create side panel for demonstration
	local sidePanel = Panel()
	sidePanel.props.title = "Info"
	sidePanel.props.layout = "vertical"
	sidePanel.props.backgroundColor = { 0.15, 0.2, 0.15, 0.8 }
	sidePanel.props.borderColor = { 0.3, 0.5, 0.3, 1 }
	sidePanel.props.padding = { top = 30, right = 15, bottom = 15, left = 15 }
	sidePanel.props.spacing = 5

	-- Add info labels to side panel
	local infoLabel1 = Label()
	infoLabel1.props.text = "Version: 0.1"
	infoLabel1.props.color = { 0.8, 0.8, 0.8, 1 }

	local infoLabel2 = Label()
	infoLabel2.props.text = "Build: Dev"
	infoLabel2.props.color = { 0.8, 0.8, 0.8, 1 }

	sidePanel:addChild(infoLabel1)
	sidePanel:addChild(infoLabel2)

	-- Add side panel to root scene
	rootScene:addChild(sidePanel, {
		x = 450,
		y = 100,
		w = 200,
		h = 150
	})

	return rootScene
end
