# Hierarchical UI System Implementation Guide

## Overview

This guide shows you how to implement a hierarchical UI system in your LuaStation13 project where you can have:

**Root Scene** → **Container Components** → **Child Components** → **Nested Children**

## Current Working Implementation

The system is now working with a simple approach using Inky directly. Here's how it works:

### 1. Basic Structure

```lua
-- Create Inky scene and pointer
local scene = Inky.scene()
local pointer = Inky.pointer(scene)

-- Create UI elements with the scene
local button = Button(scene)
local label = Label(scene)

-- Return scene object with management functions
return {
    scene = scene,
    pointer = pointer,
    elements = { button = button, label = label },
    
    setPointerPosition = function(self, x, y)
        self.pointer:setPosition(x, y)
    end,
    
    render = function(self, x, y, w, h, depth)
        self.scene:beginFrame()
        -- Render elements with manual positioning
        self.elements.button:render(x, y, w, h, 1)
        self.scene:finishFrame()
    end
}
```

### 2. Manual Hierarchy with Containers

For more complex hierarchies, you can create container objects that manage their children:

```lua
local function createContainer(name, x, y, w, h, backgroundColor)
    return {
        name = name,
        x = x, y = y, w = w, h = h,
        backgroundColor = backgroundColor,
        children = {},
        
        addChild = function(self, child, childX, childY, childW, childH)
            child.x = self.x + (childX or 0)
            child.y = self.y + (childY or 0)
            child.w = childW or child.w or 100
            child.h = childH or child.h or 30
            table.insert(self.children, child)
        end,
        
        render = function(self)
            -- Draw container background
            if self.backgroundColor then
                love.graphics.setColor(self.backgroundColor)
                love.graphics.rectangle("fill", self.x, self.y, self.w, self.h)
            end
            
            -- Render children
            for _, child in ipairs(self.children) do
                if child.element then
                    child.element:render(child.x, child.y, child.w, child.h, 1)
                end
            end
        end
    }
end
```

### 3. Building Complex Hierarchies

```lua
-- Create root container
local rootContainer = createContainer("Root", 0, 0, 800, 600, {0.1, 0.1, 0.15, 1})

-- Create header container
local headerContainer = createContainer("Header", 0, 0, 800, 80, {0.15, 0.15, 0.2, 0.9})

-- Create header elements
local titleLabel = Label(scene)
titleLabel.props.text = "My Game"

-- Add title to header
headerContainer:addChild({ element = titleLabel }, 10, 20, 780, 40)

-- Add header to root
rootContainer:addChild(headerContainer)
```

## File Structure

Your current working files:

- ✅ `client/ui/init.lua` - Main UI initialization
- ✅ `client/ui/manager.lua` - UI scene management
- ✅ `client/ui/controls/label.lua` - Text component
- ✅ `client/ui/controls/button.lua` - Interactive button
- ✅ `client/ui/scenes/menu.lua` - Working menu scene
- ✅ `client/ui/examples/simple_hierarchy.lua` - Complete hierarchy example

## Integration with Game States

Your menu state is already integrated:

```lua
-- In MenuState:enter()
LS13.UI.manager:setCurrentScene("menu")

-- In MenuState:draw()
LS13.UI.render()

-- Input handling
function MenuState:mousemoved(x, y)
    LS13.UI.handleMouse(x, y)
end

function MenuState:mousepressed(x, y, button)
    LS13.UI.handleMousePressed(x, y, button)
end
```

## Creating New Scenes

To create a new hierarchical scene:

1. **Create the scene file** (e.g., `client/ui/scenes/game.lua`):

```lua
local Inky = require("lib/Inky")

return function()
    local scene = Inky.scene()
    local pointer = Inky.pointer(scene)
    
    -- Load components
    local Button = require("client/ui/controls/button")
    local Label = require("client/ui/controls/label")
    
    -- Create elements
    local pauseButton = Button(scene)
    pauseButton.props.text = "Pause"
    
    local healthLabel = Label(scene)
    healthLabel.props.text = "Health: 100%"
    
    return {
        scene = scene,
        pointer = pointer,
        elements = { pauseButton = pauseButton, healthLabel = healthLabel },
        
        setPointerPosition = function(self, x, y)
            self.pointer:setPosition(x, y)
        end,
        
        raisePointerEvent = function(self, eventName, ...)
            self.pointer:raise(eventName, ...)
        end,
        
        render = function(self, x, y, w, h, depth)
            self.scene:beginFrame()
            
            -- Layout your elements
            self.elements.pauseButton:render(10, 10, 100, 30, 1)
            self.elements.healthLabel:render(10, 50, 200, 20, 1)
            
            self.scene:finishFrame()
        end
    }
end
```

2. **Register the scene** in `client/ui/init.lua`:

```lua
ui.scenes.Game = require("client/ui/scenes/game")
ui.manager:addScene("game", ui.scenes.Game())
```

3. **Switch to the scene** when needed:

```lua
LS13.UI.setCurrentScene("game")
```

## Layout Strategies

### Manual Layout (Current)
- Full control over positioning
- Good for complex, custom layouts
- Requires manual calculation

### Container-Based Layout
- Use the container helper functions
- Automatic child positioning
- Good for panels and grouped elements

### Grid Layout
```lua
local function layoutGrid(container, cols, spacing)
    local cellW = (container.w - spacing * (cols - 1)) / cols
    local cellH = 40 -- Fixed height
    
    for i, child in ipairs(container.children) do
        local col = (i - 1) % cols
        local row = math.floor((i - 1) / cols)
        child.x = container.x + col * (cellW + spacing)
        child.y = container.y + row * (cellH + spacing)
        child.w = cellW
        child.h = cellH
    end
end
```

## Best Practices

1. **Keep scenes focused** - One scene per game state
2. **Use containers** for grouping related elements
3. **Manual positioning** for precise control
4. **Consistent naming** for easy debugging
5. **Modular components** that can be reused

## Example Hierarchy Structure

```
Root Scene
├── Header Container
│   ├── Title Label
│   └── Menu Buttons
├── Main Container
│   ├── Left Sidebar
│   │   ├── Tool Button 1
│   │   ├── Tool Button 2
│   │   └── Tool Button 3
│   ├── Center Panel
│   │   └── Game View
│   └── Right Sidebar
│       ├── Player Info
│       └── Status Labels
└── Footer Container
    └── Status Bar
```

This system gives you the flexibility to create complex UI hierarchies while maintaining good performance and easy maintenance.

## Next Steps

1. Try the working menu scene
2. Examine `client/ui/examples/simple_hierarchy.lua` for a complete example
3. Create your own scenes using the patterns shown
4. Extend the container system as needed for your specific layouts
