# Hierarchical UI System for LuaStation13

This UI system provides a hierarchical component-based architecture using the Inky framework, allowing you to create complex UI layouts with parent-child relationships.

## Architecture Overview

The system consists of several layers:

1. **Root Scene** - The top-level container that manages the entire UI hierarchy
2. **Containers/Panels** - Intermediate components that can hold child components
3. **Leaf Components** - End components like buttons, labels, etc.

## Core Components

### Scene (`client/ui/controls/scene.lua`)
- Acts as the root container for UI hierarchies
- Manages Inky scene and pointer instances
- Handles input events and distributes them to children

### Panel (`client/ui/controls/panel.lua`)
- Styled container with layout management
- Supports multiple layout modes: `none`, `vertical`, `horizontal`, `grid`
- Has title bar, padding, margins, borders, and background styling

### Container (`client/ui/controls/container.lua`)
- Basic container with layout capabilities
- Lighter weight than Panel
- Good for grouping elements without heavy styling

### Button (`client/ui/controls/button.lua`)
- Interactive button component
- Supports hover, press, and click states
- Customizable styling and callbacks

### Label (`client/ui/controls/label.lua`)
- Text display component
- Supports alignment, colors, fonts, and shadows

## Usage Example

```lua
-- Create a hierarchical UI structure
local Scene = require("client/ui/controls/scene")
local Panel = require("client/ui/controls/panel")
local Button = require("client/ui/controls/button")
local Label = require("client/ui/controls/label")

-- Create root scene
local rootScene = Scene()
rootScene.props.name = "MyScene"
rootScene.props.backgroundColor = { 0.1, 0.1, 0.15, 1 }

-- Create main panel
local mainPanel = Panel()
mainPanel.props.title = "Main Panel"
mainPanel.props.layout = "vertical"
mainPanel.props.spacing = 10

-- Create child components
local titleLabel = Label()
titleLabel.props.text = "Welcome!"
titleLabel.props.align = "center"

local actionButton = Button()
actionButton.props.text = "Click Me"
actionButton.props.onClick = function()
    print("Button clicked!")
end

-- Build hierarchy
mainPanel:addChild(titleLabel)
mainPanel:addChild(actionButton)

rootScene:addChild(mainPanel, {
    x = 100, y = 100, w = 300, h = 200
})

-- Use the scene
ui.manager:addScene("myScene", rootScene)
ui.manager:setCurrentScene("myScene")
```

## Layout System

### Layout Types

1. **none** - Manual positioning, children use their own coordinates
2. **vertical** - Stack children vertically with equal heights
3. **horizontal** - Arrange children horizontally with equal widths  
4. **grid** - Arrange children in a square grid pattern

### Layout Properties

- `padding` - Inner spacing: `{ top, right, bottom, left }`
- `margin` - Outer spacing: `{ top, right, bottom, left }`
- `spacing` - Gap between child elements

## Input Handling

The UI Manager automatically handles input events:

```lua
-- In your main game loop
function love.mousemoved(x, y)
    ui.handleMouse(x, y)
end

function love.mousepressed(x, y, button)
    ui.handleMousePressed(x, y, button)
end

function love.mousereleased(x, y, button)
    ui.handleMouseReleased(x, y, button)
end

function love.keypressed(key, scancode, isrepeat)
    ui.handleKeyPressed(key, scancode, isrepeat)
end
```

## Rendering

```lua
-- In your love.draw() function
function love.draw()
    ui.render() -- Renders the current scene
end

-- Or with custom bounds
function love.draw()
    ui.render(0, 0, 800, 600)
end
```

## Component Properties

### Common Properties
- `visible` - Show/hide component
- `x, y, w, h` - Position and size (for manual positioning)

### Styling Properties
- `backgroundColor` - Background color `{r, g, b, a}`
- `borderColor` - Border color `{r, g, b, a}`
- `borderWidth` - Border thickness
- `cornerRadius` - Rounded corners (where supported)

### Interactive Properties (Button)
- `enabled` - Enable/disable interaction
- `onClick` - Click callback function
- `onHover` - Hover callback function
- `onPress/onRelease` - Press/release callbacks

## Advanced Usage

### Dynamic Child Management

```lua
-- Add children dynamically
local childIndex = panel:addChild(newButton, { w = 100, h = 30 })

-- Remove children
panel:removeChild(childIndex)

-- Clear all children
panel:clearChildren()

-- Get child count
local count = panel:getChildCount()
```

### Custom Styling

```lua
local styledPanel = Panel()
styledPanel.props.backgroundColor = { 0.2, 0.3, 0.4, 0.9 }
styledPanel.props.borderColor = { 0.5, 0.6, 0.7, 1 }
styledPanel.props.borderWidth = 2
styledPanel.props.cornerRadius = 5
styledPanel.props.padding = { top = 15, right = 15, bottom = 15, left = 15 }
```

### Scene Management

```lua
-- Add multiple scenes
ui.manager:addScene("menu", menuScene)
ui.manager:addScene("game", gameScene)
ui.manager:addScene("settings", settingsScene)

-- Switch between scenes
ui.setCurrentScene("menu")
ui.setCurrentScene("game")

-- Get current scene
local current = ui.getCurrentScene()
```

This hierarchical system allows you to build complex, maintainable UI structures where components can contain other components, creating a tree-like hierarchy that's easy to manage and extend.
